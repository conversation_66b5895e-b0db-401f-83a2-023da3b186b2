const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

const dbPath = path.join(__dirname, 'server/database/restaurant.db');

console.log('Fixing database encoding issues...\n');

// Backup current database
const backupPath = path.join(__dirname, 'server/database/restaurant_backup.db');
if (fs.existsSync(dbPath)) {
  fs.copyFileSync(dbPath, backupPath);
  console.log('Database backed up to restaurant_backup.db');
}

const db = new sqlite3.Database(dbPath);

// Set proper encoding
db.run("PRAGMA encoding = 'UTF-8'");
db.run("PRAGMA journal_mode = WAL");
db.run("PRAGMA synchronous = NORMAL");

// Clear corrupted data and insert fresh data
db.serialize(() => {
  // Clear categories
  db.run("DELETE FROM categories", (err) => {
    if (err) {
      console.error('Error clearing categories:', err);
      return;
    }
    console.log('Cleared existing categories');
    
    // Insert fresh categories with proper Myanmar text and icons
    const categories = [
      {
        name: 'Rice Dishes',
        name_mm: 'ထမင်းများ',
        description: 'Traditional rice-based dishes',
        description_mm: 'ရိုးရာ ထမင်းဟင်းများ',
        icon: '🍚',
        sort_order: 1
      },
      {
        name: 'Noodles',
        name_mm: 'ခေါက်ဆွဲများ',
        description: 'Various noodle dishes',
        description_mm: 'ခေါက်ဆွဲ အမျိုးမျိုး',
        icon: '🍜',
        sort_order: 2
      },
      {
        name: 'Curries',
        name_mm: 'ဟင်းများ',
        description: 'Traditional Myanmar curries',
        description_mm: 'မြန်မာ ရိုးရာ ဟင်းများ',
        icon: '🍛',
        sort_order: 3
      },
      {
        name: 'Salads',
        name_mm: 'သုပ်များ',
        description: 'Fresh salads and appetizers',
        description_mm: 'လတ်ဆတ်သော သုပ်များ',
        icon: '🥗',
        sort_order: 4
      },
      {
        name: 'Desserts',
        name_mm: 'အချိုများ',
        description: 'Traditional sweets and desserts',
        description_mm: 'ရိုးရာ အချိုပွဲများ',
        icon: '🍰',
        sort_order: 5
      },
      {
        name: 'Beverages',
        name_mm: 'ယမကာများ',
        description: 'Drinks and beverages',
        description_mm: 'အချိုရည် နှင့် ယမကာများ',
        icon: '🥤',
        sort_order: 6
      },
      {
        name: 'Snacks',
        name_mm: 'ရေစက်များ',
        description: 'Light snacks and finger foods',
        description_mm: 'ပေါ့ပါးသော ရေစက်များ',
        icon: '🍿',
        sort_order: 7
      },
      {
        name: 'Meat Dishes',
        name_mm: 'အသားများ',
        description: 'Meat-based dishes',
        description_mm: 'အသား ပါဝင်သော ဟင်းများ',
        icon: '🍖',
        sort_order: 8
      },
      {
        name: 'Seafood',
        name_mm: 'ရေထွက်ပစ္စည်းများ',
        description: 'Fresh seafood dishes',
        description_mm: 'လတ်ဆတ်သော ရေထွက်ပစ္စည်းများ',
        icon: '🐟',
        sort_order: 9
      },
      {
        name: 'Vegetables',
        name_mm: 'ဟင်းသီးဟင်းရွက်များ',
        description: 'Vegetarian dishes',
        description_mm: 'သက်သတ်လွတ် ဟင်းများ',
        icon: '🥬',
        sort_order: 10
      }
    ];
    
    let insertedCount = 0;
    categories.forEach((category, index) => {
      db.run(
        `INSERT INTO categories (name, name_mm, description, description_mm, icon, sort_order, is_active) 
         VALUES (?, ?, ?, ?, ?, ?, 1)`,
        [category.name, category.name_mm, category.description, category.description_mm, category.icon, category.sort_order],
        function(err) {
          if (err) {
            console.error('Error inserting category:', err);
          } else {
            insertedCount++;
            console.log(`Inserted category: ${category.name} (${category.name_mm}) with icon ${category.icon}`);
            
            if (insertedCount === categories.length) {
              console.log('\nAll categories inserted successfully!');
              
              // Update settings with proper Myanmar text
              const settings = [
                ['restaurant_name', 'အရှင်စားသောက်ဆိုင်', 'Restaurant name'],
                ['restaurant_name_en', 'A Shin Restaurant', 'Restaurant name in English'],
                ['restaurant_title_mm', 'မြန်မာ့အရသာ', 'Restaurant title in Myanmar'],
                ['restaurant_title_en', 'Myanmar Traditional Cuisine', 'Restaurant title in English'],
                ['restaurant_logo', '🍜', 'Restaurant logo emoji'],
                ['opening_hours_mm', '၂၄ နာရီ ဖွင့်ထားသည်', 'Opening hours in Myanmar'],
                ['opening_hours_en', 'Always Open for You', 'Opening hours in English'],
                ['welcome_message_mm', 'မင်္ဂလာပါ! ကြိုဆိုပါတယ်', 'Welcome message in Myanmar'],
                ['welcome_message_en', 'Welcome to our restaurant', 'Welcome message in English'],
                ['description_mm', 'မြန်မာ့ရိုးရာအစားအသောက်များကို အွန်လိုင်းမှ အမှာပေးနိုင်ပါတယ်', 'Restaurant description in Myanmar'],
                ['description_en', 'Order traditional Myanmar food online', 'Restaurant description in English']
              ];
              
              settings.forEach(([key, value, description]) => {
                db.run(
                  `INSERT OR REPLACE INTO settings (key, value, description) VALUES (?, ?, ?)`,
                  [key, value, description],
                  (err) => {
                    if (err) {
                      console.error('Error updating setting:', err);
                    } else {
                      console.log(`Updated setting: ${key} = ${value}`);
                    }
                  }
                );
              });
              
              setTimeout(() => {
                db.close();
                console.log('\nDatabase encoding fixed successfully!');
                console.log('Please restart the server to see the changes.');
              }, 1000);
            }
          }
        }
      );
    });
  });
});
