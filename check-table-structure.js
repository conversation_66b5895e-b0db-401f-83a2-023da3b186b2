const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'server/database/restaurant.db');
const db = new sqlite3.Database(dbPath);

console.log('Checking table structure...\n');

// Check categories table structure
db.all("PRAGMA table_info(categories)", (err, columns) => {
  if (err) {
    console.error('Error getting table info:', err);
    return;
  }
  
  console.log('=== CATEGORIES TABLE STRUCTURE ===');
  columns.forEach(col => {
    console.log(`${col.name}: ${col.type} (${col.notnull ? 'NOT NULL' : 'NULL'}) ${col.dflt_value ? 'DEFAULT ' + col.dflt_value : ''}`);
  });
  
  // Check menu_items table structure
  db.all("PRAGMA table_info(menu_items)", (err, menuColumns) => {
    if (err) {
      console.error('Error getting menu_items table info:', err);
      return;
    }
    
    console.log('\n=== MENU_ITEMS TABLE STRUCTURE ===');
    menuColumns.forEach(col => {
      console.log(`${col.name}: ${col.type} (${col.notnull ? 'NOT NULL' : 'NULL'}) ${col.dflt_value ? 'DEFAULT ' + col.dflt_value : ''}`);
    });
    
    db.close();
  });
});
