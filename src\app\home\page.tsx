'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

interface TodaySpecialItem {
  id: number;
  name: string;
  name_mm: string;
  price: number;
  image_url?: string;
  category_name_mm?: string;
}

interface Settings {
  restaurant_name?: any;
  restaurant_phone?: any;
  restaurant_address?: any;
  restaurant_description?: any;
}

export default function CustomerHomePage() {
  const searchParams = useSearchParams();
  const tableNumber = searchParams.get('table');
  
  const [mounted, setMounted] = useState(false);
  const [todaySpecials, setTodaySpecials] = useState<TodaySpecialItem[]>([]);
  const [settings, setSettings] = useState<Settings>({});
  const [cartCount, setCartCount] = useState(0);

  useEffect(() => {
    setMounted(true);
    fetchTodaySpecials();
    fetchSettings();
    loadCartCount();
  }, []);

  const fetchTodaySpecials = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/menu/today-specials');
      if (response.ok) {
        const data = await response.json();
        setTodaySpecials(data.slice(0, 4)); // Show only 4 items
      }
    } catch (error) {
      console.error('Error fetching today specials:', error);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/settings');
      if (response.ok) {
        const data = await response.json();
        const settingsObj: Settings = {};
        data.forEach((setting: any) => {
          settingsObj[setting.key as keyof Settings] = setting;
        });
        setSettings(settingsObj);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const loadCartCount = () => {
    if (typeof window !== 'undefined') {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      const totalItems = cart.reduce((sum: number, item: any) => sum + item.quantity, 0);
      setCartCount(totalItems);
    }
  };

  const getSettingValue = (setting: any) => {
    if (typeof setting === 'object' && setting?.value) {
      return setting.value;
    }
    return setting || '';
  };

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Header */}
      <div className="bg-gray-800/80 backdrop-blur-sm border-b border-gray-700 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Restaurant Logo & Name */}
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center text-white font-bold text-xl shadow-lg">
                🍽️
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  {getSettingValue(settings.restaurant_name) || 'Myanmar Taste Restaurant'}
                </h1>
                <p className="text-gray-300 text-sm">Myanmar Traditional Cuisine</p>
              </div>
            </div>

            {/* Table Info */}
            {tableNumber && (
              <div className="bg-blue-600/20 border border-blue-500/30 rounded-lg px-4 py-2">
                <div className="text-blue-400 text-sm font-medium">စားပွဲ: {tableNumber}</div>
                <div className="text-blue-300 text-xs">Table Number: {tableNumber}</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            🙏 ကြိုဆိုပါတယ်! Welcome!
          </h2>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            {getSettingValue(settings.restaurant_description) || 'မြန်မာ့ရိုးရာ အစားအသောက်များကို အရသာရှိရှိ ခံစားနိုင်ပါတယ်'}
          </p>
        </div>

        {/* Menu Button */}
        <div className="text-center mb-8">
          <Link 
            href={`/menu${tableNumber ? `?table=${tableNumber}` : ''}`}
            className="inline-flex items-center space-x-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            <span className="text-2xl">📋</span>
            <span>Menu ကြည့်ရန်</span>
            <span className="text-2xl">→</span>
          </Link>
        </div>

        {/* Today's Specials Preview */}
        {todaySpecials.length > 0 && (
          <div className="mb-8">
            <h3 className="text-2xl font-bold text-white mb-6 text-center">
              ⭐ ယနေ့ အထူးမှတ်တမ်း
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {todaySpecials.map((item) => (
                <div key={item.id} className="bg-gray-800/60 backdrop-blur-sm rounded-xl border border-gray-700 overflow-hidden hover:border-yellow-500/50 transition-all duration-300">
                  <div className="h-32 bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
                    {item.image_url ? (
                      <img 
                        src={item.image_url} 
                        alt={item.name_mm}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="text-4xl">🍽️</div>
                    )}
                  </div>
                  <div className="p-4">
                    <h4 className="font-bold text-white text-sm mb-1">{item.name_mm}</h4>
                    <p className="text-yellow-400 font-bold">{item.price.toLocaleString()} ကျပ်</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Restaurant Info */}
        <div className="bg-gray-800/60 backdrop-blur-sm rounded-xl border border-gray-700 p-6 mb-8">
          <h3 className="text-xl font-bold text-white mb-4">📍 ဆိုင်အချက်အလက်</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <span className="text-blue-400">📞</span>
              <span className="text-gray-300">
                {getSettingValue(settings.restaurant_phone) || '09-123-456-789'}
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <span className="text-green-400">📍</span>
              <span className="text-gray-300">
                {getSettingValue(settings.restaurant_address) || 'Yangon, Myanmar'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Customer Action Buttons - Fixed Bottom */}
      <div className="fixed bottom-0 left-0 right-0 bg-gray-800/95 backdrop-blur-sm border-t border-gray-700 p-4 z-50">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-4 gap-3">
            {/* Home Button */}
            <button className="bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg transition-colors text-sm font-medium flex flex-col items-center space-y-1">
              <span className="text-lg">🏠</span>
              <span>ပင်မစာမျက်နှာ</span>
            </button>

            {/* Cart Button */}
            <Link 
              href="/checkout"
              className="bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors text-sm font-medium flex flex-col items-center space-y-1 relative"
            >
              <span className="text-lg">🛒</span>
              <span>အော်ဒါ ({cartCount})</span>
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartCount}
                </span>
              )}
            </Link>

            {/* Payment Button */}
            <button className="bg-orange-600 hover:bg-orange-700 text-white py-3 px-4 rounded-lg transition-colors text-sm font-medium flex flex-col items-center space-y-1">
              <span className="text-lg">💳</span>
              <span>ငွေရှင်းမယ်</span>
            </button>

            {/* Call Waiter Button */}
            <button className="bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg transition-colors text-sm font-medium flex flex-col items-center space-y-1">
              <span className="text-lg">🔔</span>
              <span>လှန်းခေါ်မယ်</span>
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Padding for Fixed Buttons */}
      <div className="h-24"></div>
    </div>
  );
}
