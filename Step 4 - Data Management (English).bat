@echo off
title Step 4 - Restaurant System Data Inserter
color 0C

echo.
echo ================================================================
echo                Step 4 - Restaurant System                     
echo                    Data Inserter                              
echo                                                               
echo    Step 4: Insert Sample Data and Test Data                  
echo ================================================================
echo.

:MENU
echo Step 4 - Data Insertion Options:
echo.
echo [1] Insert Complete Database (Recommended)
echo [2] View Current Data Status
echo [3] Reset Database
echo [4] Test Database Connection
echo [0] Back to Main Menu
echo.
set /p choice="Select option (0-4): "

if "%choice%"=="1" goto INSERT_COMPLETE
if "%choice%"=="2" goto VIEW_STATUS
if "%choice%"=="3" goto RESET_DATABASE
if "%choice%"=="4" goto TEST_CONNECTION
if "%choice%"=="0" goto EXIT
goto INVALID

:INSERT_COMPLETE
echo.
echo Step 4.1 - Setting up Complete Restaurant Database...
echo =======================================
echo This will create a complete restaurant database with:
echo    - 10 Categories with icons
echo    - 48 Menu Items with images (40 regular + 8 today specials)
echo    - 10 Tables with Myanmar names and QR codes
echo    - Restaurant settings
echo.
set /p confirm="Continue? (Y/N): "
if /i not "%confirm%"=="Y" goto MENU

echo.
echo 🔄 Running complete database setup...
echo This may take a moment...
echo.
cd /d "%~dp0"
node check-database.js
echo.
echo ✅ Complete database setup finished!
echo.
echo 📋 What was created:
echo   • 10 Categories (Rice, Noodles, Curries, Salads, Desserts, etc.)
echo   • 48 Menu Items with images (40 regular + 8 today specials)
echo   • 10 Tables with Myanmar names (စားပွဲ ၁ to စားပွဲ ၁၀)
echo   • Restaurant settings and configuration
echo.
echo 🌐 You can now open the web interface to see all data!
pause
goto MENU

:RESET_DATABASE
echo.
echo Step 4.3 - Reset Database...
echo =======================================
echo ⚠️  WARNING: This will delete all existing data!
echo.
set /p confirm="Are you sure? (Y/N): "
if /i not "%confirm%"=="Y" goto MENU

echo.
echo 🗑️  Deleting existing database...
if exist restaurant.db del restaurant.db
echo ✅ Database deleted successfully!
echo.
echo 💡 You can now run option [1] to create fresh data.
pause
goto MENU

:TEST_CONNECTION
echo.
echo Step 4.4 - Test Database Connection...
echo =======================================
echo Testing database connection and structure...
echo.
cd /d "%~dp0"
if not exist restaurant.db (
    echo ❌ Database file not found!
    echo 💡 Please run option [1] to create the database first.
    pause
    goto MENU
)

echo 🔍 Checking database structure...
node -e "const sqlite3=require('sqlite3').verbose();const db=new sqlite3.Database('restaurant.db');console.log('✅ Database connection successful!');const tables=['categories','menu_items','tables','settings'];let completed=0;tables.forEach(tableName=>{db.get('SELECT COUNT(*) as count FROM '+tableName,[],(err,result)=>{if(err){console.error('❌ Error checking '+tableName+':',err.message);}else{console.log('✅ '+tableName+': '+result.count+' records');}completed++;if(completed===tables.length){console.log('\n🎉 Database test completed!');db.close();process.exit(0);}});});"
pause
goto MENU



:VIEW_STATUS
echo.
echo Step 4.2 - Current Data Status...
echo =======================================
echo Checking current database data...
echo.
cd /d "%~dp0"
if not exist restaurant.db (
    echo ❌ Database file not found!
    echo 💡 Please run option [1] to create the database first.
    pause
    goto MENU
)

echo 📊 CURRENT DATABASE STATUS:
echo =======================================
node -e "const sqlite3=require('sqlite3').verbose();const db=new sqlite3.Database('restaurant.db');const tables=['categories','menu_items','tables','settings'];let completed=0;tables.forEach(tableName=>{db.get('SELECT COUNT(*) as count FROM '+tableName,[],(err,result)=>{if(err){console.error('❌ Error checking '+tableName+':',err.message);}else{console.log('✅ '+tableName+': '+result.count+' records');}completed++;if(completed===tables.length){console.log('\n📋 Database status check completed!');db.close();process.exit(0);}});});"
pause
goto MENU



:INVALID
echo.
echo Invalid option! Please select 0-4.
pause
goto MENU

:EXIT
echo.
echo Step 4 Complete! Returning to Main Menu...
echo Data Management completed
pause
goto :EOF
