const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'server/database/restaurant.db');
const db = new sqlite3.Database(dbPath);

console.log('Checking database content...\n');

// Check categories
db.all("SELECT id, name, name_mm, icon FROM categories ORDER BY id", (err, rows) => {
  if (err) {
    console.error('Error fetching categories:', err);
    return;
  }
  
  console.log('=== CATEGORIES ===');
  rows.forEach(row => {
    console.log(`ID: ${row.id}`);
    console.log(`Name: ${row.name}`);
    console.log(`Name MM: ${row.name_mm}`);
    console.log(`Icon: ${row.icon}`);
    console.log('---');
  });
  
  // Check menu items
  db.all("SELECT id, name, name_mm, category_id FROM menu_items ORDER BY id LIMIT 10", (err, items) => {
    if (err) {
      console.error('Error fetching menu items:', err);
      return;
    }
    
    console.log('\n=== MENU ITEMS (First 10) ===');
    items.forEach(item => {
      console.log(`ID: ${item.id}`);
      console.log(`Name: ${item.name}`);
      console.log(`Name MM: ${item.name_mm}`);
      console.log(`Category ID: ${item.category_id}`);
      console.log('---');
    });
    
    // Check settings
    db.all("SELECT key, value FROM settings WHERE key LIKE '%restaurant%' OR key LIKE '%name%'", (err, settings) => {
      if (err) {
        console.error('Error fetching settings:', err);
        return;
      }
      
      console.log('\n=== RESTAURANT SETTINGS ===');
      settings.forEach(setting => {
        console.log(`${setting.key}: ${setting.value}`);
      });
      
      db.close();
    });
  });
});
