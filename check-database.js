const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'server/database/restaurant.db');
const db = new sqlite3.Database(dbPath);

console.log('Setting up complete restaurant database...\n');

// Set proper encoding
db.run("PRAGMA encoding = 'UTF-8'");

// First check table structure
db.all("PRAGMA table_info(categories)", (err, columns) => {
  if (err) {
    console.error('Error getting table info:', err);
    return;
  }

  console.log('=== CATEGORIES TABLE STRUCTURE ===');
  columns.forEach(col => {
    console.log(`${col.name}: ${col.type}`);
  });

  // Now fix the corrupted data
  console.log('\n=== FIXING CORRUPTED DATA ===');

  // Clear and insert fresh categories with proper Myanmar text
  db.run("DELETE FROM categories", (err) => {
    if (err) {
      console.error('Error clearing categories:', err);
      return;
    }

    console.log('Cleared existing corrupted categories');

    // Insert fresh categories with proper Myanmar text and icons
    const categories = [
      { name: 'Rice Dishes', name_mm: 'ထမင်းများ', description: 'Traditional rice-based dishes', icon: '🍚', sort_order: 1 },
      { name: 'Noodles', name_mm: 'ခေါက်ဆွဲများ', description: 'Various noodle dishes', icon: '🍜', sort_order: 2 },
      { name: 'Curries', name_mm: 'ဟင်းများ', description: 'Traditional Myanmar curries', icon: '🍛', sort_order: 3 },
      { name: 'Salads', name_mm: 'သုပ်များ', description: 'Fresh salads and appetizers', icon: '🥗', sort_order: 4 },
      { name: 'Desserts', name_mm: 'အချိုများ', description: 'Traditional sweets and desserts', icon: '🍰', sort_order: 5 },
      { name: 'Beverages', name_mm: 'ယမကာများ', description: 'Drinks and beverages', icon: '🥤', sort_order: 6 },
      { name: 'Snacks', name_mm: 'ရေစက်များ', description: 'Light snacks and finger foods', icon: '🍿', sort_order: 7 },
      { name: 'Meat Dishes', name_mm: 'အသားများ', description: 'Meat-based dishes', icon: '🍖', sort_order: 8 },
      { name: 'Seafood', name_mm: 'ရေထွက်ပစ္စည်းများ', description: 'Fresh seafood dishes', icon: '🐟', sort_order: 9 },
      { name: 'Vegetables', name_mm: 'ဟင်းသီးဟင်းရွက်များ', description: 'Vegetarian dishes', icon: '🥬', sort_order: 10 }
    ];

    let insertedCount = 0;
    categories.forEach((category) => {
      db.run(
        `INSERT INTO categories (name, name_mm, description, icon, sort_order, is_active)
         VALUES (?, ?, ?, ?, ?, 1)`,
        [category.name, category.name_mm, category.description, category.icon, category.sort_order],
        function(err) {
          if (err) {
            console.error('Error inserting category:', err);
          } else {
            insertedCount++;
            console.log(`✓ Inserted: ${category.name} (${category.name_mm}) ${category.icon}`);

            if (insertedCount === categories.length) {
              console.log('\n=== UPDATING SETTINGS ===');

              // Update settings with proper Myanmar text
              const settings = [
                ['restaurant_name', 'အရှင်စားသောက်ဆိုင်'],
                ['restaurant_name_en', 'A Shin Restaurant'],
                ['restaurant_title_mm', 'မြန်မာ့အရသာ'],
                ['restaurant_title_en', 'Myanmar Traditional Cuisine'],
                ['restaurant_logo', '🍜'],
                ['opening_hours_mm', '၂၄ နာရီ ဖွင့်ထားသည်'],
                ['opening_hours_en', 'Always Open for You'],
                ['welcome_message_mm', 'မင်္ဂလာပါ! ကြိုဆိုပါတယ်'],
                ['welcome_message_en', 'Welcome to our restaurant']
              ];

              let settingsUpdated = 0;
              settings.forEach(([key, value]) => {
                db.run(
                  `INSERT OR REPLACE INTO settings (key, value, description) VALUES (?, ?, ?)`,
                  [key, value, `${key} setting`],
                  (err) => {
                    if (err) {
                      console.error('Error updating setting:', err);
                    } else {
                      settingsUpdated++;
                      console.log(`✓ Updated: ${key} = ${value}`);

                      if (settingsUpdated === settings.length) {
                        // Add tables and menu items
                        setTimeout(() => {
                          console.log('\n=== ADDING TABLES ===');

                          // Clear existing tables
                          db.run("DELETE FROM tables", (err) => {
                            if (err) {
                              console.error('Error clearing tables:', err);
                              return;
                            }

                            // Insert tables
                            const tables = [
                              { table_number: 'T001', table_name: 'Table 1', capacity: 4, qr_code: 'QR_T001' },
                              { table_number: 'T002', table_name: 'Table 2', capacity: 4, qr_code: 'QR_T002' },
                              { table_number: 'T003', table_name: 'Table 3', capacity: 6, qr_code: 'QR_T003' },
                              { table_number: 'T004', table_name: 'Table 4', capacity: 2, qr_code: 'QR_T004' },
                              { table_number: 'T005', table_name: 'Table 5', capacity: 8, qr_code: 'QR_T005' }
                            ];

                            let tablesInserted = 0;
                            tables.forEach((table) => {
                              db.run(
                                `INSERT INTO tables (table_number, table_name, capacity, qr_code, is_active)
                                 VALUES (?, ?, ?, ?, 1)`,
                                [table.table_number, table.table_name, table.capacity, table.qr_code],
                                function(err) {
                                  if (err) {
                                    console.error('Error inserting table:', err);
                                  } else {
                                    tablesInserted++;
                                    console.log(`✓ Added: ${table.table_name} (${table.capacity} seats)`);

                                    if (tablesInserted === tables.length) {
                                      // Add menu items
                                      console.log('\n=== ADDING MENU ITEMS ===');

                                      // Clear existing menu items
                                      db.run("DELETE FROM menu_items", (err) => {
                                        if (err) {
                                          console.error('Error clearing menu items:', err);
                                          return;
                                        }

                                        // Get category IDs first
                                        db.all("SELECT id, name FROM categories ORDER BY id", (err, categories) => {
                                          if (err) {
                                            console.error('Error getting categories:', err);
                                            return;
                                          }

                                          const categoryMap = {};
                                          categories.forEach(cat => {
                                            categoryMap[cat.name] = cat.id;
                                          });

                                          // Sample menu items
                                          const menuItems = [
                                            // Rice Dishes
                                            { name: 'Chicken Fried Rice', name_mm: 'ကြက်သားကြော်ထမင်း', price: 3500, category: 'Rice Dishes', image_url: 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400' },
                                            { name: 'Pork Fried Rice', name_mm: 'ဝက်သားကြော်ထမင်း', price: 3500, category: 'Rice Dishes', image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400' },
                                            { name: 'Seafood Fried Rice', name_mm: 'ပင်လယ်စာကြော်ထမင်း', price: 4000, category: 'Rice Dishes', image_url: 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400' },
                                            { name: 'Vegetable Fried Rice', name_mm: 'ဟင်းသီးဟင်းရွက်ကြော်ထမင်း', price: 3000, category: 'Rice Dishes', image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400' },

                                            // Noodles
                                            { name: 'Mohinga', name_mm: 'မုန့်ဟင်းခါး', price: 2000, category: 'Noodles', image_url: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400' },
                                            { name: 'Shan Noodles', name_mm: 'ရှမ်းခေါက်ဆွဲ', price: 2500, category: 'Noodles', image_url: 'https://images.unsplash.com/photo-1555126634-323283e090fa?w=400' },
                                            { name: 'Coconut Noodles', name_mm: 'အုန်းနို့ခေါက်ဆွဲ', price: 3000, category: 'Noodles', image_url: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400' },
                                            { name: 'Mandalay Noodles', name_mm: 'မန္တလေးခေါက်ဆွဲ', price: 2800, category: 'Noodles', image_url: 'https://images.unsplash.com/photo-1555126634-323283e090fa?w=400' },

                                            // Curries
                                            { name: 'Chicken Curry', name_mm: 'ကြက်သားဟင်း', price: 4000, category: 'Curries', image_url: 'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400' },
                                            { name: 'Pork Curry', name_mm: 'ဝက်သားဟင်း', price: 4500, category: 'Curries', image_url: 'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400' },
                                            { name: 'Fish Curry', name_mm: 'ငါးဟင်း', price: 5000, category: 'Curries', image_url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400' },
                                            { name: 'Beef Curry', name_mm: 'နွားသားဟင်း', price: 5500, category: 'Curries', image_url: 'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400' },

                                            // Salads
                                            { name: 'Tea Leaf Salad', name_mm: 'လပက်သုပ်', price: 3000, category: 'Salads', image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400' },
                                            { name: 'Ginger Salad', name_mm: 'ချင်းသုပ်', price: 2500, category: 'Salads', image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400' },
                                            { name: 'Tomato Salad', name_mm: 'ခရမ်းချဉ်သီးသုပ်', price: 2000, category: 'Salads', image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400' },
                                            { name: 'Cucumber Salad', name_mm: 'သခွားသီးသုပ်', price: 1800, category: 'Salads', image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400' },

                                            // Desserts
                                            { name: 'Shwe Yin Aye', name_mm: 'ရွှေရင်အေး', price: 2000, category: 'Desserts', image_url: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400' },
                                            { name: 'Mont Lone Yay Paw', name_mm: 'မုန့်လုံးရေပေါ်', price: 1500, category: 'Desserts', image_url: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400' },
                                            { name: 'Coconut Pudding', name_mm: 'အုန်းပေါင်း', price: 1800, category: 'Desserts', image_url: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400' },
                                            { name: 'Sticky Rice', name_mm: 'ကောက်ညှင်းထမင်း', price: 2200, category: 'Desserts', image_url: 'https://images.unsplash.com/photo-1586190848861-99aa4a171e90?w=400' },

                                            // Beverages
                                            { name: 'Myanmar Tea', name_mm: 'မြန်မာလက်ဖက်ရည်', price: 800, category: 'Beverages', image_url: 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400' },
                                            { name: 'Coffee', name_mm: 'ကော်ဖီ', price: 1200, category: 'Beverages', image_url: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400' },
                                            { name: 'Fresh Lime Juice', name_mm: 'သံပုရာရည်', price: 1500, category: 'Beverages', image_url: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400' },
                                            { name: 'Sugarcane Juice', name_mm: 'ကြံရည်', price: 1800, category: 'Beverages', image_url: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400' },

                                            // Snacks
                                            { name: 'Samosa', name_mm: 'ဆမူဆာ', price: 500, category: 'Snacks', image_url: 'https://images.unsplash.com/photo-1601050690597-df0568f70950?w=400' },
                                            { name: 'Spring Rolls', name_mm: 'ကော်ပြန့်', price: 800, category: 'Snacks', image_url: 'https://images.unsplash.com/photo-1601050690597-df0568f70950?w=400' },
                                            { name: 'Fried Tofu', name_mm: 'တိုဖူးကြော်', price: 1000, category: 'Snacks', image_url: 'https://images.unsplash.com/photo-1601050690597-df0568f70950?w=400' },
                                            { name: 'Fish Cake', name_mm: 'ငါးလုံး', price: 1200, category: 'Snacks', image_url: 'https://images.unsplash.com/photo-1601050690597-df0568f70950?w=400' },

                                            // Meat Dishes
                                            { name: 'BBQ Pork', name_mm: 'ဝက်သားကင်', price: 6000, category: 'Meat Dishes', image_url: 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?w=400' },
                                            { name: 'Grilled Chicken', name_mm: 'ကြက်သားကင်', price: 5500, category: 'Meat Dishes', image_url: 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?w=400' },
                                            { name: 'Beef Steak', name_mm: 'နွားသားစတိတ်', price: 8000, category: 'Meat Dishes', image_url: 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?w=400' },
                                            { name: 'Pork Ribs', name_mm: 'ဝက်သားနံရိုး', price: 7000, category: 'Meat Dishes', image_url: 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?w=400' },

                                            // Seafood
                                            { name: 'Grilled Fish', name_mm: 'ငါးကင်', price: 6500, category: 'Seafood', image_url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400' },
                                            { name: 'Fried Prawns', name_mm: 'ပုစွန်ကြော်', price: 7500, category: 'Seafood', image_url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400' },
                                            { name: 'Crab Curry', name_mm: 'ကဏန်းဟင်း', price: 9000, category: 'Seafood', image_url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400' },
                                            { name: 'Fish Soup', name_mm: 'ငါးချဉ်းစိမ်', price: 4500, category: 'Seafood', image_url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400' },

                                            // Vegetables
                                            { name: 'Stir-fried Vegetables', name_mm: 'ဟင်းသီးဟင်းရွက်ကြော်', price: 2500, category: 'Vegetables', image_url: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400' },
                                            { name: 'Eggplant Curry', name_mm: 'ခရမ်းသီးဟင်း', price: 3000, category: 'Vegetables', image_url: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400' },
                                            { name: 'Gourd Soup', name_mm: 'ဗူးသီးဟင်းချို', price: 2800, category: 'Vegetables', image_url: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400' },
                                            { name: 'Bean Sprout Salad', name_mm: 'ပဲပင်စိမ်းသုပ်', price: 2200, category: 'Vegetables', image_url: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400' }
                                          ];

                                          let menuInserted = 0;
                                          menuItems.forEach((item) => {
                                            const categoryId = categoryMap[item.category];
                                            if (categoryId) {
                                              db.run(
                                                `INSERT INTO menu_items (name, name_mm, price, category_id, is_active, is_available)
                                                 VALUES (?, ?, ?, ?, 1, 1)`,
                                                [item.name, item.name_mm, item.price, categoryId],
                                                function(err) {
                                                  if (err) {
                                                    console.error('Error inserting menu item:', err);
                                                  } else {
                                                    menuInserted++;
                                                    console.log(`✓ Added: ${item.name} (${item.name_mm}) - ${item.price} MMK`);

                                                    if (menuInserted === menuItems.length) {
                                                      // Add Today Special Menu Items to menu_items table
                                                      console.log('\n=== ADDING TODAY SPECIAL MENU ITEMS ===');

                                                      // Today Special Menu Items (8 items)
                                                      const todaySpecials = [
                                                        {
                                                          name: 'Special Mohinga',
                                                          name_mm: 'အထူးမုန့်ဟင်းခါး',
                                                          description: 'Traditional fish soup with rice noodles and special herbs',
                                                          description_mm: 'ရိုးရာငါးချဉ်းစိမ်နဲ့ ခေါက်ဆွဲ အထူးဟင်းရွက်များပါ',
                                                          price: 2500,
                                                          category: 'Noodles',
                                                          image_url: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400'
                                                        },
                                                        {
                                                          name: 'Royal Shan Noodles',
                                                          name_mm: 'ဘုရင့်ရှမ်းခေါက်ဆွဲ',
                                                          description: 'Premium Shan noodles with special chicken curry',
                                                          description_mm: 'အထူးကြက်သားဟင်းနဲ့ ရှမ်းခေါက်ဆွဲ',
                                                          price: 3500,
                                                          category: 'Noodles',
                                                          image_url: 'https://images.unsplash.com/photo-1555126634-323283e090fa?w=400'
                                                        },
                                                        {
                                                          name: 'Premium Tea Leaf Salad',
                                                          name_mm: 'အထူးလပက်သုပ်',
                                                          description: 'Traditional tea leaf salad with premium ingredients',
                                                          description_mm: 'အရည်အသွေးမြင့် ပစ္စည်းများနဲ့ လပက်သုပ်',
                                                          price: 4000,
                                                          category: 'Salads',
                                                          image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400'
                                                        },
                                                        {
                                                          name: 'Special Chicken Curry',
                                                          name_mm: 'အထူးကြက်သားဟင်း',
                                                          description: 'Slow-cooked chicken curry with aromatic spices',
                                                          description_mm: 'အနံ့သာမြန်မာ့အမွှေးအကြိုင်များနဲ့ ကြက်သားဟင်း',
                                                          price: 5000,
                                                          category: 'Curries',
                                                          image_url: 'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400'
                                                        },
                                                        {
                                                          name: 'Grilled Fish Special',
                                                          name_mm: 'အထူးငါးကင်',
                                                          description: 'Fresh grilled fish with special Myanmar sauce',
                                                          description_mm: 'လတ်ဆတ်သော ငါးကင်နဲ့ မြန်မာ့အထူးဆော့စ်',
                                                          price: 7500,
                                                          category: 'Seafood',
                                                          image_url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400'
                                                        },
                                                        {
                                                          name: 'Royal Fried Rice',
                                                          name_mm: 'ဘုရင့်ကြော်ထမင်း',
                                                          description: 'Premium fried rice with mixed seafood and meat',
                                                          description_mm: 'ပင်လယ်စာနဲ့ အသားများ ရောစပ်ထားသော ကြော်ထမင်း',
                                                          price: 4500,
                                                          category: 'Rice Dishes',
                                                          image_url: 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400'
                                                        },
                                                        {
                                                          name: 'Special BBQ Platter',
                                                          name_mm: 'အထူးကင်စားပွဲ',
                                                          description: 'Mixed BBQ platter with chicken, pork and beef',
                                                          description_mm: 'ကြက်သား၊ ဝက်သား၊ နွားသား ရောစပ်ကင်စားပွဲ',
                                                          price: 8500,
                                                          category: 'Meat Dishes',
                                                          image_url: 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?w=400'
                                                        },
                                                        {
                                                          name: 'Golden Shwe Yin Aye',
                                                          name_mm: 'ရွှေရင်အေးရွှေ',
                                                          description: 'Premium dessert with coconut milk and special toppings',
                                                          description_mm: 'အုန်းနို့နဲ့ အထူးတင်ဆက်မှုများပါသော အချိုပွဲ',
                                                          price: 3000,
                                                          category: 'Desserts',
                                                          image_url: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400'
                                                        }
                                                      ];

                                                      let specialsInserted = 0;
                                                      todaySpecials.forEach((special) => {
                                                        const categoryId = categoryMap[special.category];
                                                        if (categoryId) {
                                                          db.run(
                                                            `INSERT INTO menu_items (name, name_mm, description, description_mm, price, category_id, image_url, is_active, is_available, is_today_special)
                                                             VALUES (?, ?, ?, ?, ?, ?, ?, 1, 1, 1)`,
                                                            [special.name, special.name_mm, special.description, special.description_mm, special.price, categoryId, special.image_url],
                                                            function(err) {
                                                              if (err) {
                                                                console.error('Error inserting today special:', err);
                                                              } else {
                                                                specialsInserted++;
                                                                console.log(`⭐ Added Special: ${special.name} (${special.name_mm}) - ${special.price} MMK`);

                                                                if (specialsInserted === todaySpecials.length) {
                                                                  // Final verification
                                                                  setTimeout(() => {
                                                                    console.log('\n=== FINAL VERIFICATION ===');
                                                                    db.all("SELECT COUNT(*) as count FROM categories", (err, catCount) => {
                                                                      if (!err) console.log(`Categories: ${catCount[0].count}`);
                                                                    });
                                                                    db.all("SELECT COUNT(*) as count FROM tables", (err, tableCount) => {
                                                                      if (!err) console.log(`Tables: ${tableCount[0].count}`);
                                                                    });
                                                                    db.all("SELECT COUNT(*) as count FROM menu_items", (err, menuCount) => {
                                                                      if (!err) console.log(`Menu Items: ${menuCount[0].count}`);
                                                                    });
                                                                    db.all("SELECT COUNT(*) as count FROM menu_items WHERE is_today_special = 1", (err, specialCount) => {
                                                                      if (!err) console.log(`Today Specials: ${specialCount[0].count}`);
                                                                    });

                                                                    console.log('\n✅ Database setup completed successfully!');
                                                                    console.log('🔄 Please restart the server to see all changes.');
                                                                    db.close();
                                                                  }, 500);
                                                                }
                                                              }
                                                            }
                                                          );
                                                        }
                                                      });
                                                    }
                                                  }
                                                }
                                              );
                                            }
                                          });
                                        });
                                      });
                                    }
                                  }
                                }
                              );
                            });
                          });
                        }, 500);
                      }
                    }
                  }
                );
              });
            }
          }
        }
      );
    });
  });
});
