const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'server/database/restaurant.db');
const db = new sqlite3.Database(dbPath);

console.log('Checking and fixing database encoding issues...\n');

// Set proper encoding
db.run("PRAGMA encoding = 'UTF-8'");

// First check table structure
db.all("PRAGMA table_info(categories)", (err, columns) => {
  if (err) {
    console.error('Error getting table info:', err);
    return;
  }

  console.log('=== CATEGORIES TABLE STRUCTURE ===');
  columns.forEach(col => {
    console.log(`${col.name}: ${col.type}`);
  });

  // Now fix the corrupted data
  console.log('\n=== FIXING CORRUPTED DATA ===');

  // Clear and insert fresh categories with proper Myanmar text
  db.run("DELETE FROM categories", (err) => {
    if (err) {
      console.error('Error clearing categories:', err);
      return;
    }

    console.log('Cleared existing corrupted categories');

    // Insert fresh categories with proper Myanmar text and icons
    const categories = [
      { name: 'Rice Dishes', name_mm: 'ထမင်းများ', description: 'Traditional rice-based dishes', icon: '🍚', sort_order: 1 },
      { name: 'Noodles', name_mm: 'ခေါက်ဆွဲများ', description: 'Various noodle dishes', icon: '🍜', sort_order: 2 },
      { name: 'Curries', name_mm: 'ဟင်းများ', description: 'Traditional Myanmar curries', icon: '🍛', sort_order: 3 },
      { name: 'Salads', name_mm: 'သုပ်များ', description: 'Fresh salads and appetizers', icon: '🥗', sort_order: 4 },
      { name: 'Desserts', name_mm: 'အချိုများ', description: 'Traditional sweets and desserts', icon: '🍰', sort_order: 5 },
      { name: 'Beverages', name_mm: 'ယမကာများ', description: 'Drinks and beverages', icon: '🥤', sort_order: 6 },
      { name: 'Snacks', name_mm: 'ရေစက်များ', description: 'Light snacks and finger foods', icon: '🍿', sort_order: 7 },
      { name: 'Meat Dishes', name_mm: 'အသားများ', description: 'Meat-based dishes', icon: '🍖', sort_order: 8 },
      { name: 'Seafood', name_mm: 'ရေထွက်ပစ္စည်းများ', description: 'Fresh seafood dishes', icon: '🐟', sort_order: 9 },
      { name: 'Vegetables', name_mm: 'ဟင်းသီးဟင်းရွက်များ', description: 'Vegetarian dishes', icon: '🥬', sort_order: 10 }
    ];

    let insertedCount = 0;
    categories.forEach((category) => {
      db.run(
        `INSERT INTO categories (name, name_mm, description, icon, sort_order, is_active)
         VALUES (?, ?, ?, ?, ?, 1)`,
        [category.name, category.name_mm, category.description, category.icon, category.sort_order],
        function(err) {
          if (err) {
            console.error('Error inserting category:', err);
          } else {
            insertedCount++;
            console.log(`✓ Inserted: ${category.name} (${category.name_mm}) ${category.icon}`);

            if (insertedCount === categories.length) {
              console.log('\n=== UPDATING SETTINGS ===');

              // Update settings with proper Myanmar text
              const settings = [
                ['restaurant_name', 'အရှင်စားသောက်ဆိုင်'],
                ['restaurant_name_en', 'A Shin Restaurant'],
                ['restaurant_title_mm', 'မြန်မာ့အရသာ'],
                ['restaurant_title_en', 'Myanmar Traditional Cuisine'],
                ['restaurant_logo', '🍜'],
                ['opening_hours_mm', '၂၄ နာရီ ဖွင့်ထားသည်'],
                ['opening_hours_en', 'Always Open for You'],
                ['welcome_message_mm', 'မင်္ဂလာပါ! ကြိုဆိုပါတယ်'],
                ['welcome_message_en', 'Welcome to our restaurant']
              ];

              let settingsUpdated = 0;
              settings.forEach(([key, value]) => {
                db.run(
                  `INSERT OR REPLACE INTO settings (key, value, description) VALUES (?, ?, ?)`,
                  [key, value, `${key} setting`],
                  (err) => {
                    if (err) {
                      console.error('Error updating setting:', err);
                    } else {
                      settingsUpdated++;
                      console.log(`✓ Updated: ${key} = ${value}`);

                      if (settingsUpdated === settings.length) {
                        // Verify the fix
                        setTimeout(() => {
                          console.log('\n=== VERIFICATION ===');
                          db.all("SELECT id, name, name_mm, icon FROM categories ORDER BY id", (err, rows) => {
                            if (err) {
                              console.error('Error verifying categories:', err);
                              return;
                            }

                            console.log('Fixed Categories:');
                            rows.forEach(row => {
                              console.log(`${row.id}. ${row.name} (${row.name_mm}) ${row.icon}`);
                            });

                            console.log('\n✅ Database encoding fixed successfully!');
                            console.log('🔄 Please restart the server to see the changes.');
                            db.close();
                          });
                        }, 500);
                      }
                    }
                  }
                );
              });
            }
          }
        }
      );
    });
  });
});
