'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import Navigation from '../components/Navigation';

interface TodaySpecialItem {
  id: number;
  name: string;
  name_mm: string;
  price: number;
  image_url?: string;
  category_name_mm?: string;
}

interface Category {
  id: number;
  name: string;
  name_mm: string;
  icon?: string;
  items?: any[];
}

export default function HomePage() {
  const searchParams = useSearchParams();
  const tableNumber = searchParams.get('table');

  const [mounted, setMounted] = useState(false);
  const [todaySpecials, setTodaySpecials] = useState<TodaySpecialItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [settings, setSettings] = useState<any>({});
  const [cartCount, setCartCount] = useState(0);

  const getCategoryIcon = (categoryName: string, dbIcon?: string) => {
    // Database မှာ သိမ်းထားတဲ့ icon ကို ဦးစားပေးသုံးမယ်
    if (dbIcon && dbIcon.trim() !== '') {
      return dbIcon;
    }

    const iconMap: { [key: string]: string } = {
      // Text-based icons for categories (Priority mapping)
      '[RICE]': '🍚',
      '[NOODLE]': '🍜',
      '[CURRY]': '🍛',
      '[SALAD]': '🥗',
      '[DESSERT]': '🍰',
      '[DRINK]': '🥤',

      // Debug: Log what we're checking
      ...(process.env.NODE_ENV === 'development' && console.log('Checking icon for:', categoryName, 'dbIcon:', dbIcon) || {}),

      // Food Categories (Myanmar)
      'ထမင်းများ': '🍚',
      'ခေါက်ဆွဲများ': '🍜',
      'ဟင်းများ': '🍛',
      'သုပ်များ': '🥗',
      'အချိုများ': '🍰',
      'ယမကာများ': '🥤',

      // English equivalents
      'Rice Dishes': '🍚',
      'Noodles': '🍜',
      'Curries': '🍛',
      'Salads': '🥗',
      'Desserts': '🍰',
      'Drinks': '🥤',

      'မုန့်များ': '🥖',
      'အသားများ': '🍖',
      'ကြက်သားများ': '🍗',
      'ငါးများ': '🐟',
      'ပင်လယ်စာများ': '🦐',
      'ဟင်းသီးဟင်းရွက်များ': '🥬',
      'သစ်သီးများ': '🍎',
      'ပေါင်မုန့်များ': '🍞',
      'ကော်ဖီ': '☕',
      'လက်ဖက်ရည်': '🍵',

      // Utensils & Accessories
      'ပန်းကန်များ': '🍽️',
      'ဇွန်းများ': '🥄',
      'ခက်ရင်းများ': '🍴',
      'ဖန်ခွက်များ': '🥃',
      'အရက်ခွက်များ': '🍻',
      'တစ်ရှူးများ': '🧻',
      'ထုပ်ပိုးများ': '🥡',
      'သန့်ရှင်းရေးပစ္စည်းများ': '🧽',
      'တူများ': '🥢',
      'ရေခွက်များ': '🍶',
      'အိတ်များ': '🛍️',
      'အထွေထွေ': '🍽️'
    };

    // First check database icon for text-based icons
    if (dbIcon && dbIcon.trim()) {
      // Check if it's a text-based icon
      if (iconMap[dbIcon]) {
        return iconMap[dbIcon];
      }
      // If it's not corrupted (no weird characters), use it
      if (!dbIcon.includes('ƒ') && !dbIcon.includes('≡') && !dbIcon.includes('�')) {
        return dbIcon;
      }
    }

    // Check for exact matches by category name
    if (iconMap[categoryName]) {
      return iconMap[categoryName];
    }

    // Check for partial matches
    for (const [key, icon] of Object.entries(iconMap)) {
      if (categoryName.includes(key.replace('များ', '')) || categoryName.includes(key)) {
        return icon;
      }
    }

    return '🍽️'; // Default icon
  };

  useEffect(() => {
    setMounted(true);
    fetchTodaySpecials();
    fetchCategories();
    fetchSettings();
    loadCartCount();
  }, []);

  const loadCartCount = () => {
    if (typeof window !== 'undefined') {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      const totalItems = cart.reduce((sum: number, item: any) => sum + item.quantity, 0);
      setCartCount(totalItems);
    }
  };

  const fetchTodaySpecials = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/menu/today-special');
      if (response.ok) {
        const data = await response.json();
        setTodaySpecials(data); // Show all today's special items
      }
    } catch (error) {
      console.error('Error fetching today specials:', error);
      // Fallback data if API fails
      setTodaySpecials([
        { id: 1, name: 'Mohinga', name_mm: 'မုန့်ဟင်းခါး', price: 2000, category_name_mm: 'မုန့်များ' },
        { id: 2, name: 'Shan Noodles', name_mm: 'ရှမ်းခေါက်ဆွဲ', price: 2500, category_name_mm: 'ခေါက်ဆွဲများ' },
        { id: 3, name: 'Tea Leaf Salad', name_mm: 'လပက်သုပ်', price: 3000, category_name_mm: 'သုပ်များ' },
        { id: 4, name: 'Coconut Rice', name_mm: 'အုန်းထမင်း', price: 1800, category_name_mm: 'ထမင်းများ' }
      ]);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/menu/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data.slice(0, 8)); // Show only first 8 categories
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  if (!mounted) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto mb-4"></div>
          <p className="text-orange-700 font-medium">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 relative overflow-hidden">
      {/* Background Food Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 left-10 text-6xl">🍜</div>
        <div className="absolute top-20 right-20 text-4xl">🍛</div>
        <div className="absolute top-40 left-1/4 text-5xl">🥘</div>
        <div className="absolute top-60 right-1/3 text-3xl">🍲</div>
        <div className="absolute bottom-40 left-20 text-4xl">🥗</div>
        <div className="absolute bottom-20 right-10 text-5xl">🍱</div>
        <div className="absolute bottom-60 left-1/2 text-3xl">🍳</div>
        <div className="absolute top-1/2 left-10 text-4xl">🥟</div>
        <div className="absolute top-1/3 right-10 text-3xl">🍤</div>
        <div className="absolute top-80 left-1/3 text-4xl">🍝</div>
        <div className="absolute bottom-80 right-1/4 text-3xl">🥙</div>
        <div className="absolute top-1/4 left-1/2 text-5xl">🍕</div>
      </div>

      {/* Navigation - Hide nav links on homepage */}
      <Navigation settings={settings} showNavLinks={false} />

      {/* Welcome Section */}
      <div className="max-w-4xl mx-auto px-4 py-4 text-center relative z-10">
        <div className="mb-4">
          <h2 className="text-lg font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-2">
            🙏 မင်္ဂလာပါ! ကြိုဆိုပါတယ် 🙏
          </h2>
          <p className="text-sm text-gray-300 font-medium">
            မြန်မာ့ရိုးရာအစားအသောက်များကို အွန်လိုင်းမှ အမှာပေးနိုင်ပါတယ်
          </p>
          <p className="text-xs text-gray-400 mt-1">
            Order traditional Myanmar food online
          </p>
        </div>

        {/* Customer Menu Section */}
        <div className="mb-6">
          <Link href="/menu" className="group block">
            <div className="bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border-2 border-blue-600 hover:border-blue-400 transform hover:scale-105 max-w-md mx-auto">
              <div className="text-center">
                <div className="text-4xl mb-4 group-hover:animate-bounce">🍽️</div>
                <h3 className="text-xl font-bold text-blue-400 mb-2">မီနူးကြည့်ရန်</h3>
                <p className="text-blue-300 font-medium text-sm">အစားအသောက်များကြည့်ပြီး အမှာပေးရန်</p>
                <p className="text-blue-400 text-xs mt-1">Browse menu and place orders</p>
                <div className="mt-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-2 px-4 rounded-xl font-bold text-sm group-hover:from-blue-600 group-hover:to-blue-700 transition-all duration-300">
                  🚀 မီနူး ကြည့်ရန်
                </div>
              </div>
            </div>
          </Link>
        </div>

        {/* Features Section */}
        <div className="bg-gray-800/90 backdrop-blur-sm rounded-xl p-4 border border-gray-700 shadow-lg">
          <h3 className="text-base font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-3 text-center">
            🌟 ကျွန်ုပ်တို့၏ ဝန်ဆောင်မှုများ
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-center">
            <div className="bg-gradient-to-br from-blue-800/50 to-blue-700/50 p-3 rounded-lg border border-blue-600">
              <div className="text-xl mb-1">📱</div>
              <p className="text-blue-300 font-bold text-xs mb-1">QR Code</p>
              <p className="text-blue-400 text-xs">QR ကုဒ်ဖြင့် အမှာပေးရန်</p>
            </div>
            <div className="bg-gradient-to-br from-green-800/50 to-green-700/50 p-3 rounded-lg border border-green-600">
              <div className="text-xl mb-1">⚡</div>
              <p className="text-green-300 font-bold text-xs mb-1">Fast Service</p>
              <p className="text-green-400 text-xs">လျင်မြန်သော ဝန်ဆောင်မှု</p>
            </div>
            <div className="bg-gradient-to-br from-orange-800/50 to-orange-700/50 p-3 rounded-lg border border-orange-600">
              <div className="text-xl mb-1">🍜</div>
              <p className="text-orange-300 font-bold text-xs mb-1">Traditional</p>
              <p className="text-orange-400 text-xs">မြန်မာ့ရိုးရာ အစားအသောက်</p>
            </div>
            <div className="bg-gradient-to-br from-yellow-800/50 to-yellow-700/50 p-3 rounded-lg border border-yellow-600">
              <div className="text-xl mb-1">💰</div>
              <p className="text-yellow-300 font-bold text-xs mb-1">Fair Price</p>
              <p className="text-yellow-400 text-xs">သင့်တော်သော ဈေးနှုန်း</p>
            </div>
          </div>
        </div>

        {/* Categories Section */}
        <div className="mb-8">
          <div className="bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
            <div className="text-center mb-6">
              <div className="flex items-center justify-center space-x-2 mb-3">
                <div className="bg-blue-500/20 rounded-full p-2">
                  <span className="text-xl">📂</span>
                </div>
                <h3 className="text-xl font-bold text-white">
                  အမျိုးအစားများ
                </h3>
                <div className="bg-blue-500/20 text-blue-300 text-sm px-3 py-1 rounded-full font-bold">
                  {categories.length} ခု
                </div>
              </div>
              <p className="text-gray-300 text-sm">Food Categories</p>
            </div>

            {categories.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                {categories.map((category) => (
                  <Link
                    key={category.id}
                    href={`/menu?category=${category.id}`}
                    className="group"
                  >
                    <div className="bg-gray-700/80 backdrop-blur-sm rounded-xl p-4 border border-gray-600 hover:border-blue-500 transition-all duration-300 hover:shadow-lg transform hover:scale-105">
                      <div className="text-center">
                        <div className="text-3xl mb-3 group-hover:animate-bounce">
                          {getCategoryIcon(category.name_mm, category.icon)}
                        </div>
                        <h4 className="text-sm font-bold text-white mb-1 line-clamp-1">
                          {category.name_mm}
                        </h4>
                        <p className="text-xs text-gray-300 line-clamp-1">
                          {category.name}
                        </p>
                        <div className="mt-2 text-xs text-blue-400">
                          {category.items?.length || 0} မီနူး
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-4xl mb-3">📂</div>
                <p className="text-gray-300 font-medium text-sm mb-2">အမျိုးအစားများ မရှိသေးပါ</p>
                <p className="text-gray-400 text-xs">Admin မှ အမျိုးအစားများ ထည့်သွင်းမည်</p>
              </div>
            )}

            <div className="text-center">
              <Link
                href="/menu"
                className="inline-block bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-2 rounded-lg font-bold text-sm hover:from-blue-600 hover:to-purple-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105"
              >
                🍽️ မီနူးအပြည့်အစုံ ကြည့်ရန်
              </Link>
            </div>
          </div>
        </div>

        {/* Today's Special Section */}
        <div className="mt-6 bg-gradient-to-r from-yellow-800/30 via-orange-800/30 to-red-800/30 rounded-xl p-4 border border-yellow-600 shadow-lg">
          <div className="text-center mb-4">
            <div className="flex items-center justify-center space-x-2 mb-3">
              <div className="bg-yellow-500/30 rounded-full p-2 animate-pulse">
                <span className="text-xl">⭐</span>
              </div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-yellow-400 to-red-400 bg-clip-text text-transparent">
                ယနေ့အထူး မီနူးများ
              </h3>
              <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white text-sm px-3 py-1 rounded-full font-bold shadow-lg">
                {todaySpecials.length} ခု
              </div>
            </div>
            <p className="text-orange-300 font-medium text-xs mb-2">Today's Special Menu</p>
            <div className="text-xs text-yellow-200">
              {todaySpecials.length > 0
                ? `အထူးစျေးနှုန်းဖြင့် ရရှိနိုင်သော မီနူးများ`
                : 'Admin မှ ယနေ့အထူး မီနူးများ ထည့်သွင်းမည်'
              }
            </div>
          </div>

          {/* Today's Special Items Grid */}
          {todaySpecials.length > 0 ? (
            <div className={`grid gap-3 mb-4 ${
              todaySpecials.length === 1 ? 'grid-cols-1 justify-items-center' :
              todaySpecials.length === 2 ? 'grid-cols-2' :
              todaySpecials.length === 3 ? 'grid-cols-3' :
              todaySpecials.length >= 4 ? 'grid-cols-2 md:grid-cols-4' :
              'grid-cols-2 md:grid-cols-4'
            }`}>
              {todaySpecials.map((item, index) => (
                <div key={item.id} className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-yellow-600 hover:border-yellow-400 transition-all duration-300 hover:shadow-md">
                  <div className="text-center">
                    <div className="text-2xl mb-2">
                      {getCategoryIcon(item.category_name_mm || 'အထွေထွေ')}
                    </div>
                    <h4 className="text-xs font-bold text-yellow-300 mb-1 line-clamp-1">{item.name_mm}</h4>
                    <p className="text-xs text-yellow-400 mb-1 line-clamp-1">{item.name}</p>
                    <p className="text-xs font-bold text-orange-300">{item.price.toLocaleString()} ကျပ်</p>
                    {item.category_name_mm && (
                      <p className="text-xs text-gray-400 mt-1">{item.category_name_mm}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 mb-4">
              <div className="text-4xl mb-3">🍽️</div>
              <p className="text-yellow-300 font-medium text-sm mb-2">ယနေ့အထူး မီနူးများ မရှိသေးပါ</p>
              <p className="text-orange-400 text-xs">Admin မှ ယနေ့အထူး မီနူးများ ထည့်သွင်းမည်</p>
            </div>
          )}

          <div className="text-center">
            <Link
              href="/menu"
              className="inline-block bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-4 py-2 rounded-lg font-bold text-xs hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105"
            >
              🍽️ ယနေ့အထူးများ အားလုံး ကြည့်ရန်
            </Link>
          </div>
        </div>

        {/* Customer Action Buttons - Only show if table parameter exists */}
        {tableNumber && (
          <div className="mt-6 mb-6">
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 border border-gray-700 shadow-md">
              <div className="text-center mb-3">
                <p className="text-blue-400 font-bold text-sm">စားပွဲ: {tableNumber}</p>
                <p className="text-gray-300 text-xs">Table Number: {tableNumber}</p>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {/* Home Button */}
                <button className="bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded-lg transition-colors text-sm font-medium flex flex-col items-center space-y-1">
                  <span className="text-lg">🏠</span>
                  <span>ပင်မစာမျက်နှာ</span>
                </button>

                {/* Cart Button */}
                <Link
                  href="/checkout"
                  className="bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg transition-colors text-sm font-medium flex flex-col items-center space-y-1 relative"
                >
                  <span className="text-lg">🛒</span>
                  <span>အော်ဒါ ({cartCount})</span>
                  {cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-white text-blue-600 text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                      •
                    </span>
                  )}
                </Link>

                {/* Payment Button */}
                <button className="bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors text-sm font-medium flex flex-col items-center space-y-1">
                  <span className="text-lg">💳</span>
                  <span>ငွေရှင်းမယ်</span>
                </button>

                {/* Call Waiter Button */}
                <button className="bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg transition-colors text-sm font-medium flex flex-col items-center space-y-1">
                  <span className="text-lg">🔔</span>
                  <span>လှန်းခေါ်မယ်</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Footer Info */}
        <div className="mt-6 text-center">
          <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl p-3 border border-gray-700 shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div>
                <p className="text-orange-300 font-bold text-xs mb-1">🌐 Local Network</p>
                <p className="text-gray-300 text-xs">localhost:3000</p>
                <p className="text-gray-400 text-xs">WiFi ကွန်ယက်မှ အသုံးပြုနိုင်ပါတယ်</p>
              </div>
              <div>
                <p className="text-orange-300 font-bold text-xs mb-1">📞 ဆက်သွယ်ရန်</p>
                <p className="text-gray-300 text-xs">09-123-456-789</p>
                <p className="text-gray-400 text-xs">24/7 အမှာလက်ခံပါတယ်</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
