const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'server/database/restaurant.db');
const db = new sqlite3.Database(dbPath);

console.log('Adding more tables with Myanmar names...\n');

// Set proper encoding
db.run("PRAGMA encoding = 'UTF-8'");

// Clear existing tables and add 10 tables with Myanmar names
db.run("DELETE FROM tables", (err) => {
  if (err) {
    console.error('Error clearing tables:', err);
    return;
  }
  
  console.log('Cleared existing tables');
  
  // Insert 10 tables with Myanmar names and QR codes
  const tables = [
    { table_number: 'T001', table_name: 'စားပွဲ ၁', capacity: 4, qr_code: 'T001-175078786434' },
    { table_number: 'T002', table_name: 'စားပွဲ ၂', capacity: 4, qr_code: 'T002-175078786435' },
    { table_number: 'T003', table_name: 'စားပွဲ ၃', capacity: 6, qr_code: 'T003-175078786436' },
    { table_number: 'T004', table_name: 'စားပွဲ ၄', capacity: 4, qr_code: 'T004-175078786437' },
    { table_number: 'T005', table_name: 'စားပွဲ ၅', capacity: 2, qr_code: 'T005-175078786438' },
    { table_number: 'T006', table_name: 'စားပွဲ ၆', capacity: 4, qr_code: 'T006-175078786439' },
    { table_number: 'T007', table_name: 'စားပွဲ ၇', capacity: 8, qr_code: 'T007-175078786440' },
    { table_number: 'T008', table_name: 'စားပွဲ ၈', capacity: 4, qr_code: 'T008-175078786441' },
    { table_number: 'T009', table_name: 'စားပွဲ ၉', capacity: 6, qr_code: 'T009-175078786442' },
    { table_number: 'T010', table_name: 'စားပွဲ ၁၀', capacity: 4, qr_code: 'T010-175078786443' }
  ];
  
  let tablesInserted = 0;
  tables.forEach((table) => {
    db.run(
      `INSERT INTO tables (table_number, table_name, capacity, qr_code, is_active) 
       VALUES (?, ?, ?, ?, 1)`,
      [table.table_number, table.table_name, table.capacity, table.qr_code],
      function(err) {
        if (err) {
          console.error('Error inserting table:', err);
        } else {
          tablesInserted++;
          console.log(`✓ Added: ${table.table_name} (${table.capacity} ဦး) - QR: ${table.qr_code}`);
          
          if (tablesInserted === tables.length) {
            // Verify the tables
            setTimeout(() => {
              console.log('\n=== VERIFICATION ===');
              db.all("SELECT table_number, table_name, capacity, qr_code FROM tables ORDER BY table_number", (err, rows) => {
                if (err) {
                  console.error('Error verifying tables:', err);
                  return;
                }
                
                console.log('All Tables:');
                rows.forEach(row => {
                  console.log(`${row.table_number}: ${row.table_name} (${row.capacity} ဦး) - ${row.qr_code}`);
                });
                
                console.log('\n✅ Tables updated successfully!');
                console.log('🔄 Please restart the server to see the changes.');
                db.close();
              });
            }, 500);
          }
        }
      }
    );
  });
});
